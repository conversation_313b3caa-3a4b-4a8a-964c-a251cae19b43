{% extends 'base.html' %}

{% block title %}Produtos - Alto da Serra{% endblock %}

{% endblock %}

{% block content %}
<div class="col-12">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center my-4">
        <h2><i class="bi bi-box-seam"></i> Catálogo de Produtos</h2>
        <button class="btn btn-primary" onclick="refreshData()">
            <i class="bi bi-arrow-clockwise"></i> Atualizar
        </button>
    </div>

    <!-- Statistics -->
    <div class="row">
        <div class="col-md-4">
            <div class="stats-card">
                <h3>{{ produtos|length }}</h3>
                <p class="mb-0">Total de Produtos</p>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stats-card">
                <h3 id="preco-medio">R$ 0,00</h3>
                <p class="mb-0">Preço Médio</p>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stats-card">
                <h3 id="produto-mais-caro">R$ 0,00</h3>
                <p class="mb-0">Produto Mais Caro</p>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="search-section">
        <div class="row">
            <div class="col-md-6">
                <label class="form-label">Buscar Produto:</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                    <input type="text" class="form-control" id="busca-produto" 
                           placeholder="Digite o nome do produto...">
                </div>
            </div>
            <div class="col-md-3">
                <label class="form-label">Ordenar por:</label>
                <select class="form-select" id="ordenacao">
                    <option value="nome">Nome (A-Z)</option>
                    <option value="nome-desc">Nome (Z-A)</option>
                    <option value="preco">Preço (Menor)</option>
                    <option value="preco-desc">Preço (Maior)</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button class="btn btn-outline-primary w-100" onclick="aplicarFiltros()">
                    <i class="bi bi-funnel"></i> Aplicar
                </button>
            </div>
        </div>
    </div>

    <!-- Produtos Grid -->
    <div class="row" id="produtos-container">
        {% for produto in produtos %}
        <div class="col-lg-4 col-md-6 produto-item" 
             data-nome="{{ produto.nome|lower }}" 
             data-preco="{{ produto.preco }}">
            <div class="card produto-card">
                <div class="produto-header text-center">
                    <i class="bi bi-box-seam display-4 mb-2"></i>
                    <h5 class="mb-0">{{ produto.nome }}</h5>
                </div>
                <div class="card-body text-center">
                    <div class="preco-tag d-inline-block mb-3">
                        R$ {{ produto.preco|floatformat:2 }}
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" onclick="adicionarAoPedido({{ produto.id }}, '{{ produto.nome }}', {{ produto.preco }})">
                            <i class="bi bi-plus-circle"></i> Adicionar ao Pedido
                        </button>
                        <button class="btn btn-outline-info" onclick="verDetalhes({{ produto.id }})">
                            <i class="bi bi-info-circle"></i> Ver Detalhes
                        </button>
                    </div>
                </div>
                <div class="card-footer text-center bg-transparent">
                    <small class="text-muted">ID: {{ produto.id }}</small>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="alert alert-info text-center">
                <i class="bi bi-info-circle"></i> Nenhum produto encontrado.
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Modal Adicionar ao Pedido -->
<div class="modal fade" id="modalAdicionarPedido" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Adicionar ao Pedido</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <i class="bi bi-box-seam display-4 text-primary"></i>
                    <h4 id="produto-nome-modal"></h4>
                    <p class="text-muted">Preço unitário: <span id="produto-preco-modal"></span></p>
                </div>
                
                <form id="formAdicionarPedido">
                    <div class="mb-3">
                        <label class="form-label">Selecionar Mesa:</label>
                        <select class="form-select" id="mesa-select" required>
                            <option value="">Selecione uma mesa...</option>
                            <!-- Mesas serão carregadas via JavaScript -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Quantidade:</label>
                        <div class="input-group">
                            <button class="btn btn-outline-secondary" type="button" onclick="alterarQuantidade(-1)">
                                <i class="bi bi-dash"></i>
                            </button>
                            <input type="number" class="form-control text-center" id="quantidade-modal" min="1" value="1">
                            <button class="btn btn-outline-secondary" type="button" onclick="alterarQuantidade(1)">
                                <i class="bi bi-plus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Total:</label>
                        <input type="text" class="form-control" id="total-modal" readonly>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="confirmarPedido()">
                    <i class="bi bi-check-circle"></i> Confirmar Pedido
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let produtoSelecionado = null;
    
    // Calcular estatísticas
    function calcularEstatisticas() {
        const produtos = document.querySelectorAll('.produto-item');
        let precos = [];
        
        produtos.forEach(produto => {
            const preco = parseFloat(produto.dataset.preco);
            precos.push(preco);
        });
        
        if (precos.length > 0) {
            const precoMedio = precos.reduce((a, b) => a + b, 0) / precos.length;
            const precoMaisCaro = Math.max(...precos);
            
            document.getElementById('preco-medio').textContent = `R$ ${precoMedio.toFixed(2).replace('.', ',')}`;
            document.getElementById('produto-mais-caro').textContent = `R$ ${precoMaisCaro.toFixed(2).replace('.', ',')}`;
        }
    }
    
    // Aplicar filtros
    function aplicarFiltros() {
        const busca = document.getElementById('busca-produto').value.toLowerCase();
        const ordenacao = document.getElementById('ordenacao').value;
        const produtos = Array.from(document.querySelectorAll('.produto-item'));
        
        // Filtrar por busca
        produtos.forEach(produto => {
            const nome = produto.dataset.nome;
            produto.style.display = nome.includes(busca) ? 'block' : 'none';
        });
        
        // Ordenar
        const produtosVisiveis = produtos.filter(p => p.style.display !== 'none');
        produtosVisiveis.sort((a, b) => {
            switch(ordenacao) {
                case 'nome':
                    return a.dataset.nome.localeCompare(b.dataset.nome);
                case 'nome-desc':
                    return b.dataset.nome.localeCompare(a.dataset.nome);
                case 'preco':
                    return parseFloat(a.dataset.preco) - parseFloat(b.dataset.preco);
                case 'preco-desc':
                    return parseFloat(b.dataset.preco) - parseFloat(a.dataset.preco);
                default:
                    return 0;
            }
        });
        
        // Reordenar no DOM
        const container = document.getElementById('produtos-container');
        produtosVisiveis.forEach(produto => container.appendChild(produto));
    }
    
    // Adicionar ao pedido
    function adicionarAoPedido(id, nome, preco) {
        produtoSelecionado = { id, nome, preco };
        document.getElementById('produto-nome-modal').textContent = nome;
        document.getElementById('produto-preco-modal').textContent = `R$ ${preco.toFixed(2).replace('.', ',')}`;
        
        calcularTotalModal();
        carregarMesas();
        
        new bootstrap.Modal(document.getElementById('modalAdicionarPedido')).show();
    }
    
    // Alterar quantidade
    function alterarQuantidade(delta) {
        const input = document.getElementById('quantidade-modal');
        const novaQuantidade = Math.max(1, parseInt(input.value) + delta);
        input.value = novaQuantidade;
        calcularTotalModal();
    }
    
    // Calcular total do modal
    function calcularTotalModal() {
        const quantidade = parseInt(document.getElementById('quantidade-modal').value) || 1;
        const preco = produtoSelecionado ? produtoSelecionado.preco : 0;
        const total = preco * quantidade;
        
        document.getElementById('total-modal').value = `R$ ${total.toFixed(2).replace('.', ',')}`;
    }
    
    // Carregar mesas (simulado)
    function carregarMesas() {
        const select = document.getElementById('mesa-select');
        select.innerHTML = '<option value="">Selecione uma mesa...</option>';
        
        // Aqui você faria uma chamada AJAX para buscar as mesas
        for (let i = 1; i <= 30; i++) {
            const option = document.createElement('option');
            option.value = `M${i}`;
            option.textContent = `Mesa ${i}`;
            select.appendChild(option);
        }
    }
    
    // Confirmar pedido
    function confirmarPedido() {
        const mesaId = document.getElementById('mesa-select').value;
        const quantidade = parseInt(document.getElementById('quantidade-modal').value);
        
        if (!mesaId || !quantidade || !produtoSelecionado) {
            alert('Por favor, preencha todos os campos.');
            return;
        }
        
        console.log('Confirmar pedido:', {
            produtoId: produtoSelecionado.id,
            mesaId: mesaId,
            quantidade: quantidade
        });
        
        // Aqui você faria a chamada AJAX para adicionar o pedido
        alert('Pedido adicionado com sucesso!');
        bootstrap.Modal.getInstance(document.getElementById('modalAdicionarPedido')).hide();
    }
    
    // Ver detalhes
    function verDetalhes(id) {
        alert(`Ver detalhes do produto ID: ${id}`);
        // Implementar modal de detalhes ou redirecionar
    }
    
    // Atualizar dados
    function refreshData() {
        location.reload();
    }
    
    // Event listeners
    document.getElementById('busca-produto').addEventListener('input', aplicarFiltros);
    document.getElementById('ordenacao').addEventListener('change', aplicarFiltros);
    document.getElementById('quantidade-modal').addEventListener('input', calcularTotalModal);
    
    // Inicializar
    document.addEventListener('DOMContentLoaded', function() {
        calcularEstatisticas();
    });
</script>
{% endblock %}
