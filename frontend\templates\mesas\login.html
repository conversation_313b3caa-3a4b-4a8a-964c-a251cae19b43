<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - <PERSON></title>
    
    <!-- Favicon -->
    {% load static %}
    <link rel="icon" type="image/png" href="{% static 'images/logofornecedor.png' %}">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Inter Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-blue: #1e3a8a;
            --primary-blue-light: #3b82f6;
            --primary-blue-lighter: #60a5fa;
            --primary-gradient: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%);
            --white: #ffffff;
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-800: #1e293b;
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --radius-lg: 1rem;
            --radius-xl: 1.5rem;
            --radius-2xl: 2rem;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        body {
            background: var(--primary-gradient);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Inter', sans-serif;
            position: relative;
            overflow: hidden;
        }
        
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
        
        .login-container {
            position: relative;
            z-index: 1;
            width: 100%;
            max-width: 450px;
            padding: 2rem;
        }
        
        .login-card {
            background: var(--white);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-2xl);
            overflow: hidden;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .login-header {
            background: var(--primary-gradient);
            color: var(--white);
            padding: 3rem 2rem 2rem;
            text-align: center;
            position: relative;
        }
        
        .login-header::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 20px;
            background: var(--white);
            border-radius: 20px 20px 0 0;
        }
        
        .logo-container {
            width: 100px;
            height: 100px;
            background: var(--white);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            box-shadow: var(--shadow-xl);
            padding: 1rem;
        }
        
        .logo-container img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 50%;
        }
        
        .login-body {
            padding: 2rem;
            background: var(--white);
        }
        
        .form-floating {
            margin-bottom: 1.5rem;
        }
        
        .form-control {
            border-radius: var(--radius-lg);
            border: 2px solid var(--gray-200);
            padding: 1rem 1.25rem;
            font-size: 1rem;
            transition: var(--transition);
            background: var(--gray-50);
        }
        
        .form-control:focus {
            border-color: var(--primary-blue-light);
            box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.15);
            background: var(--white);
        }
        
        .form-floating > label {
            color: var(--gray-800);
            font-weight: 500;
        }
        
        .btn-login {
            background: var(--primary-gradient);
            border: none;
            border-radius: var(--radius-lg);
            padding: 1rem 2rem;
            font-weight: 700;
            font-size: 1rem;
            width: 100%;
            color: var(--white);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        
        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn-login:hover::before {
            left: 100%;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
            color: var(--white);
        }
        
        .system-title {
            font-size: 1.75rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .system-subtitle {
            font-size: 1rem;
            font-weight: 400;
            opacity: 0.9;
            margin-bottom: 0;
        }
        
        .alert {
            border-radius: var(--radius-lg);
            border: none;
            margin-bottom: 1.5rem;
        }
        
        @media (max-width: 576px) {
            .login-container {
                padding: 1rem;
            }
            
            .login-header {
                padding: 2rem 1.5rem 1.5rem;
            }
            
            .login-body {
                padding: 1.5rem;
            }
            
            .logo-container {
                width: 80px;
                height: 80px;
            }
            
            .system-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="logo-container">
                    <img src="{% static 'images/logocliente.png' %}" alt="Alto da Serra">
                </div>
                <h1 class="system-title">Alto da Serra</h1>
                <p class="system-subtitle">Sistema de Gestão Profissional</p>
            </div>
            
            <div class="login-body">
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
                
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="form-floating">
                        <input type="text" class="form-control" id="email" name="email" placeholder="Usuário" required>
                        <label for="email"><i class="bi bi-person-fill me-2"></i>Usuário</label>
                    </div>
                    
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password" placeholder="Senha" required>
                        <label for="password"><i class="bi bi-lock-fill me-2"></i>Senha</label>
                    </div>
                    
                    <button type="submit" class="btn btn-login">
                        <i class="bi bi-box-arrow-in-right me-2"></i>
                        Entrar no Sistema
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="text-center mt-4">
            <div class="d-flex align-items-center justify-content-center text-white" style="opacity: 0.8;">
                <img src="{% static 'images/logofornecedor.png' %}" alt="LIM Soluções" height="24" class="me-2" style="border-radius: 4px;">
                <small>Desenvolvido por <strong>LIM Soluções</strong></small>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
