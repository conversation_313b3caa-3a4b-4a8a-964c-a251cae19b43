{% extends 'base.html' %}

{% block title %}Mesa {{ mesa.nome }} - <PERSON>{% endblock %}

{% block extra_css %}
<style>
    .mesa-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .consumo-item {
        border-left: 4px solid #007bff;
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }
    .consumo-item:hover {
        background-color: #e9ecef;
        transform: translateX(5px);
    }
    .action-buttons {
        gap: 0.5rem;
    }
    .total-section {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
    }
    .status-badge {
        font-size: 1rem;
        padding: 0.5rem 1rem;
        border-radius: 25px;
    }
    .btn-action {
        border-radius: 20px;
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="col-12">
    <!-- <PERSON>er -->
    <div class="mesa-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2><i class="bi bi-table"></i> {{ mesa.nome }}</h2>
                {% if mesa.cliente %}
                    <p class="mb-1"><i class="bi bi-person"></i> Cliente: {{ mesa.cliente }}</p>
                {% endif %}
                {% if mesa.cpf_cnpj %}
                    <p class="mb-0"><i class="bi bi-card-text"></i> CPF/CNPJ: {{ mesa.cpf_cnpj }}</p>
                {% endif %}
            </div>
            <div class="col-md-4 text-md-end">
                <span class="status-badge bg-{{ mesa.status|lower == 'livre' and 'success' or mesa.status|lower == 'ocupada' and 'danger' or 'warning' }}">
                    {{ mesa.status|title }}
                </span>
                <div class="mt-2">
                    <a href="{% url 'mesas:dashboard' %}" class="btn btn-light">
                        <i class="bi bi-arrow-left"></i> Voltar
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Consumo da Mesa -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-list-ul"></i> Consumo da Mesa</h5>
                    <button class="btn btn-primary btn-sm" onclick="abrirModalAdicionarPedido()">
                        <i class="bi bi-plus-circle"></i> Adicionar Pedido
                    </button>
                </div>
                <div class="card-body">
                    {% if consumo %}
                        {% for item in consumo %}
                        <div class="consumo-item">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h6 class="mb-1">{{ item.nome }}</h6>
                                    <small class="text-muted">R$ {{ item.preco|floatformat:2 }} cada</small>
                                </div>
                                <div class="col-md-3 text-center">
                                    <span class="badge bg-primary">Qtd: {{ item.quantidade }}</span>
                                </div>
                                <div class="col-md-3 text-end">
                                    <div class="d-flex justify-content-end action-buttons">
                                        <strong class="text-success me-2">R$ {{ item.total|floatformat:2 }}</strong>
                                        <button class="btn btn-warning btn-sm btn-action" 
                                                onclick="abrirModalPagarItem({{ item.id }}, '{{ item.nome }}', {{ item.total }})">
                                            <i class="bi bi-credit-card"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm btn-action" 
                                                onclick="abrirModalExcluirItem({{ item.id }}, '{{ item.nome }}')">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="bi bi-inbox display-4 text-muted"></i>
                            <p class="text-muted mt-2">Nenhum item no consumo desta mesa.</p>
                            <button class="btn btn-primary" onclick="abrirModalAdicionarPedido()">
                                <i class="bi bi-plus-circle"></i> Adicionar Primeiro Pedido
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Resumo e Ações -->
        <div class="col-lg-4">
            <!-- Total -->
            <div class="total-section mb-3">
                <h4><i class="bi bi-calculator"></i> Total da Mesa</h4>
                <h2 class="mb-0">R$ {{ total_consumo|floatformat:2 }}</h2>
            </div>

            <!-- Ações da Mesa -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="bi bi-gear"></i> Ações da Mesa</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-success" onclick="finalizarConta()">
                            <i class="bi bi-check-circle"></i> Finalizar Conta
                        </button>
                        <button class="btn btn-warning" onclick="abrirModalEditarMesa()">
                            <i class="bi bi-pencil"></i> Editar Mesa
                        </button>
                        <button class="btn btn-info" onclick="imprimirConta()">
                            <i class="bi bi-printer"></i> Imprimir Conta
                        </button>
                    </div>
                </div>
            </div>

            <!-- Informações Adicionais -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="bi bi-info-circle"></i> Informações</h6>
                </div>
                <div class="card-body">
                    <p><strong>Mesa ID:</strong> {{ mesa.id }}</p>
                    <p><strong>Status:</strong> {{ mesa.status|title }}</p>
                    <p><strong>Total de Itens:</strong> {{ consumo|length }}</p>
                    <p class="mb-0"><strong>Última Atualização:</strong> <span id="ultima-atualizacao"></span></p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Adicionar Pedido -->
<div class="modal fade" id="modalAdicionarPedido" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Adicionar Pedido</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="formAdicionarPedido">
                    <div class="mb-3">
                        <label class="form-label">Produto:</label>
                        <select class="form-select" id="produto-select" required>
                            <option value="">Selecione um produto...</option>
                            {% for produto in produtos %}
                            <option value="{{ produto.id }}" data-preco="{{ produto.preco }}">
                                {{ produto.nome }} - R$ {{ produto.preco|floatformat:2 }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Quantidade:</label>
                        <input type="number" class="form-control" id="quantidade-input" min="1" value="1" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Total:</label>
                        <input type="text" class="form-control" id="total-preview" readonly>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="adicionarPedido()">Adicionar</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Atualizar timestamp
    document.getElementById('ultima-atualizacao').textContent = new Date().toLocaleString('pt-BR');
    
    // Modal Adicionar Pedido
    function abrirModalAdicionarPedido() {
        new bootstrap.Modal(document.getElementById('modalAdicionarPedido')).show();
    }
    
    // Calcular total do pedido
    document.getElementById('produto-select').addEventListener('change', calcularTotal);
    document.getElementById('quantidade-input').addEventListener('input', calcularTotal);
    
    function calcularTotal() {
        const select = document.getElementById('produto-select');
        const quantidade = parseInt(document.getElementById('quantidade-input').value) || 0;
        const preco = parseFloat(select.options[select.selectedIndex]?.dataset.preco) || 0;
        const total = preco * quantidade;
        
        document.getElementById('total-preview').value = `R$ ${total.toFixed(2).replace('.', ',')}`;
    }
    
    // Adicionar pedido
    function adicionarPedido() {
        const produtoId = document.getElementById('produto-select').value;
        const quantidade = parseInt(document.getElementById('quantidade-input').value);
        
        if (!produtoId || !quantidade) {
            alert('Por favor, selecione um produto e informe a quantidade.');
            return;
        }
        
        // Aqui você faria a chamada AJAX para adicionar o pedido
        console.log('Adicionar pedido:', { produtoId, quantidade, mesaId: '{{ mesa.id }}' });
        
        // Por enquanto, apenas recarrega a página
        location.reload();
    }
    
    // Outras funções
    function abrirModalPagarItem(itemId, nome, total) {
        if (confirm(`Confirma o pagamento do item "${nome}" no valor de R$ ${total.toFixed(2).replace('.', ',')}?`)) {
            console.log('Pagar item:', itemId);
            // Implementar lógica de pagamento
        }
    }
    
    function abrirModalExcluirItem(itemId, nome) {
        if (confirm(`Confirma a exclusão do item "${nome}"?`)) {
            console.log('Excluir item:', itemId);
            // Implementar lógica de exclusão
        }
    }
    
    function finalizarConta() {
        if (confirm('Confirma a finalização da conta desta mesa?')) {
            console.log('Finalizar conta da mesa:', '{{ mesa.id }}');
            // Implementar lógica de finalização
        }
    }
    
    function abrirModalEditarMesa() {
        console.log('Editar mesa:', '{{ mesa.id }}');
        // Implementar modal de edição
    }
    
    function imprimirConta() {
        window.print();
    }
</script>
{% endblock %}
