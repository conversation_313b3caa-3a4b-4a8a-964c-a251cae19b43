{% extends 'base.html' %}

{% block title %}Cozinha - <PERSON>{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: var(--white);
        border-radius: var(--radius-xl);
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow);
        border: 1px solid var(--gray-200);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 200px;
        background: var(--primary-gradient);
        opacity: 0.05;
        border-radius: 50%;
        transform: translate(50px, -50px);
    }

    .page-title {
        color: var(--primary-blue);
        font-weight: 800;
        font-size: 2.5rem;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .page-subtitle {
        color: var(--gray-600);
        font-size: 1.1rem;
        margin-bottom: 0;
    }

    .pedido-card {
        border: 1px solid var(--gray-200);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow);
        transition: var(--transition);
        margin-bottom: 1.5rem;
        background: var(--white);
        overflow: hidden;
        position: relative;
    }

    .pedido-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-xl);
    }

    .pedido-pendente {
        border-left: 6px solid #dc3545;
        background: linear-gradient(135deg, rgba(220, 53, 69, 0.05) 0%, rgba(220, 53, 69, 0.1) 100%);
    }

    .pedido-preparo {
        border-left: 6px solid #ffc107;
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.05) 0%, rgba(255, 193, 7, 0.1) 100%);
    }

    .pedido-pronto {
        border-left: 6px solid #28a745;
        background: linear-gradient(135deg, rgba(40, 167, 69, 0.05) 0%, rgba(40, 167, 69, 0.1) 100%);
    }

    .status-badge {
        padding: 0.75rem 1.25rem;
        border-radius: var(--radius-xl);
        font-weight: 700;
        text-transform: uppercase;
        font-size: 0.8rem;
        letter-spacing: 0.05em;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        box-shadow: var(--shadow);
    }

    .status-pendente {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: var(--white);
    }

    .status-preparo {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        color: #212529;
    }

    .status-pronto {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        color: var(--white);
    }

    .mesa-info {
        background: var(--primary-gradient);
        color: var(--white);
        border-radius: var(--radius-lg);
        padding: 1.5rem;
        margin-bottom: 1rem;
        position: relative;
        overflow: hidden;
    }

    .mesa-info::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: translate(30px, -30px);
    }

    .mesa-info h5 {
        font-weight: 700;
        font-size: 1.25rem;
        margin-bottom: 0.5rem;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    .pedido-header {
        padding: 1.5rem;
        border-bottom: 1px solid var(--gray-200);
    }

    .pedido-body {
        padding: 1.5rem;
    }

    .pedido-item {
        background: var(--gray-50);
        border: 1px solid var(--gray-200);
        border-radius: var(--radius-lg);
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .pedido-item:last-child {
        margin-bottom: 0;
    }

    .pedido-item h6 {
        color: var(--primary-blue);
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .tempo-pedido {
        background: var(--secondary-gradient);
        color: var(--white);
        padding: 0.5rem 1rem;
        border-radius: var(--radius-lg);
        font-weight: 600;
        font-size: 0.9rem;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-status {
        border-radius: var(--radius-lg);
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        transition: var(--transition);
        margin: 0.25rem;
    }

    .btn-status:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .filters-section {
        background: var(--white);
        border: 1px solid var(--gray-200);
        border-radius: var(--radius-xl);
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow);
    }

    .filter-title {
        color: var(--primary-blue);
        font-weight: 700;
        font-size: 1.25rem;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .tempo-pedido {
        font-size: 0.9rem;
        color: #6c757d;
    }
    
    .item-pedido {
        background: rgba(255,255,255,0.8);
        border-radius: 8px;
        padding: 0.75rem;
        margin: 0.5rem 0;
        border-left: 3px solid #007bff;
    }
    
    .btn-acao {
        border-radius: 25px;
        padding: 0.5rem 1.5rem;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.8rem;
        letter-spacing: 0.5px;
        margin: 0.25rem;
    }
    
    .stats-container {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin: 0;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
        margin: 0;
    }
    
    .column-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem;
        border-radius: 10px;
        text-align: center;
        margin-bottom: 1rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="bi bi-egg-fried"></i> Cozinha
                    </h1>
                    <p class="text-muted mb-0">Gestão de pedidos e preparo</p>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="atualizarPedidos()">
                        <i class="bi bi-arrow-clockwise"></i> Atualizar
                    </button>
                    <button class="btn btn-success" onclick="marcarTodosProntos()">
                        <i class="bi bi-check-all"></i> Marcar Todos Prontos
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Estatísticas -->
    <div class="stats-container">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number text-danger" id="total-pendentes">0</div>
                    <div class="stat-label">Pedidos Pendentes</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number text-warning" id="total-preparo">0</div>
                    <div class="stat-label">Em Preparo</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number text-success" id="total-prontos">0</div>
                    <div class="stat-label">Prontos</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number text-info" id="tempo-medio">0min</div>
                    <div class="stat-label">Tempo Médio</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Colunas de Pedidos -->
    <div class="row">
        <!-- Pedidos Pendentes -->
        <div class="col-lg-4">
            <div class="column-header">
                <i class="bi bi-clock"></i> Pendentes
            </div>
            <div id="pedidos-pendentes">
                <!-- Pedidos pendentes serão carregados aqui -->
            </div>
        </div>

        <!-- Pedidos em Preparo -->
        <div class="col-lg-4">
            <div class="column-header">
                <i class="bi bi-fire"></i> Em Preparo
            </div>
            <div id="pedidos-preparo">
                <!-- Pedidos em preparo serão carregados aqui -->
            </div>
        </div>

        <!-- Pedidos Prontos -->
        <div class="col-lg-4">
            <div class="column-header">
                <i class="bi bi-check-circle"></i> Prontos
            </div>
            <div id="pedidos-prontos">
                <!-- Pedidos prontos serão carregados aqui -->
            </div>
        </div>
    </div>

    <!-- Loading -->
    <div id="loading" class="text-center py-5" style="display: none;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Carregando...</span>
        </div>
        <p class="mt-2 text-muted">Carregando pedidos...</p>
    </div>

    <!-- Mensagem quando não há pedidos -->
    <div id="sem-pedidos" class="text-center py-5" style="display: none;">
        <i class="bi bi-cup-hot display-1 text-muted"></i>
        <h4 class="text-muted mt-3">Nenhum pedido na cozinha</h4>
        <p class="text-muted">Todos os pedidos foram finalizados.</p>
    </div>
</div>

<!-- Modal de Detalhes do Pedido -->
<div class="modal fade" id="modalDetalhesPedido" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-receipt"></i> Detalhes do Pedido
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="modal-body-detalhes">
                <!-- Detalhes serão carregados aqui -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                <button type="button" class="btn btn-warning" onclick="iniciarPreparo()">
                    <i class="bi bi-play"></i> Iniciar Preparo
                </button>
                <button type="button" class="btn btn-success" onclick="marcarPronto()">
                    <i class="bi bi-check"></i> Marcar Pronto
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% load static %}
<script src="{% static 'js/cozinha.js' %}"></script>
{% endblock %}
