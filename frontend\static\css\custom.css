/* Custom CSS for Alto da <PERSON> Restaurant Management */

/* Global Styles */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    --warning-gradient: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    --danger-gradient: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 30px rgba(0,0,0,0.2);
    --border-radius: 15px;
    --transition: all 0.3s ease;
}

/* Body and Layout */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.card:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

/* Mesa Cards */
.mesa-card {
    cursor: pointer;
    height: 100%;
    min-height: 200px;
}

.mesa-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.mesa-card .card-body {
    padding: 1.5rem;
}

/* Status Badges */
.status-badge {
    font-size: 0.85rem;
    font-weight: 600;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-livre {
    background: var(--success-gradient);
    color: white;
}

.status-ocupada {
    background: var(--danger-gradient);
    color: white;
}

.status-reservada {
    background: var(--warning-gradient);
    color: white;
}

/* Buttons */
.btn {
    border-radius: 25px;
    font-weight: 500;
    padding: 0.6rem 1.2rem;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: var(--primary-gradient);
}

.btn-success {
    background: var(--success-gradient);
}

.btn-warning {
    background: var(--warning-gradient);
}

.btn-danger {
    background: var(--danger-gradient);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* Statistics Cards */
.stats-card {
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.stats-card h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card p {
    font-size: 1rem;
    opacity: 0.9;
}

/* Filters Section */
.filters-section {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-light);
}

/* Mesa Header */
.mesa-header {
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-light);
}

/* Consumo Items */
.consumo-item {
    border-left: 4px solid #007bff;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: var(--transition);
}

.consumo-item:hover {
    background-color: #e9ecef;
    transform: translateX(5px);
    box-shadow: var(--shadow-light);
}

/* Action Buttons */
.action-buttons {
    gap: 0.5rem;
}

.btn-action {
    border-radius: 20px;
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
    min-width: 40px;
}

/* Total Section */
.total-section {
    background: var(--success-gradient);
    color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--shadow-light);
}

.total-section h2 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 0;
}

/* Modals */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
}

.modal-header {
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* Forms */
.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Notifications */
.alert {
    border: none;
    border-radius: 10px;
    font-weight: 500;
}

/* Navigation */
.navbar {
    box-shadow: var(--shadow-light);
}

/* Clock */
.clock {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .mesa-header {
        padding: 1.5rem;
        text-align: center;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .filters-section {
        padding: 1.5rem;
    }
    
    .consumo-item {
        padding: 0.75rem;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .btn-action {
        width: 100%;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Print Styles */
@media print {
    .btn, .modal, .navbar, .filters-section {
        display: none !important;
    }
    
    .mesa-header {
        background: #333 !important;
        color: white !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
