/* <PERSON> - Professional Restaurant Management System */

/* Global Color Palette - Blue Theme */
:root {
    /* Primary Blue Palette */
    --primary-blue: #1e3a8a;           /* Deep Blue */
    --primary-blue-light: #3b82f6;     /* Bright Blue */
    --primary-blue-lighter: #60a5fa;   /* Light Blue */
    --primary-blue-dark: #1e40af;      /* Dark Blue */
    --primary-blue-darker: #1e3a8a;    /* Darker Blue */

    /* Secondary Blues */
    --secondary-blue: #0ea5e9;          /* Sky Blue */
    --secondary-blue-light: #38bdf8;    /* Light Sky Blue */
    --accent-blue: #06b6d4;             /* <PERSON>an <PERSON> */

    /* Gradients */
    --primary-gradient: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%);
    --secondary-gradient: linear-gradient(135deg, #0ea5e9 0%, #38bdf8 100%);
    --accent-gradient: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    --success-gradient: linear-gradient(135deg, #059669 0%, #10b981 100%);
    --warning-gradient: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
    --danger-gradient: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);

    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-blue: 0 10px 25px -5px rgba(30, 58, 138, 0.3);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
    --radius-2xl: 2rem;

    /* Transitions */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Body and Layout */
body {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    color: var(--gray-800);
    line-height: 1.6;
    min-height: 100vh;
}

/* Import Inter Font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Cards */
.card {
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    transition: var(--transition);
    background: var(--white);
    backdrop-filter: blur(10px);
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
    border-color: var(--primary-blue-lighter);
}

.card-header {
    background: var(--primary-gradient);
    color: var(--white);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0 !important;
    border: none;
    font-weight: 600;
    padding: 1.25rem 1.5rem;
    font-size: 1.1rem;
}

.card-body {
    padding: 1.5rem;
}

/* Mesa Cards */
.mesa-card, .card-mesa {
    cursor: pointer;
    height: 100%;
    min-height: 200px;
    position: relative;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    transition: var(--transition);
    overflow: hidden;
}

.mesa-card:hover, .card-mesa:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-blue);
}

.mesa-card::before, .card-mesa::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    opacity: 0;
    transition: var(--transition);
}

.mesa-card:hover::before, .card-mesa:hover::before {
    opacity: 1;
}

.mesa-card .card-body {
    padding: 1.5rem;
}

/* Status Badges */
.status-badge {
    font-size: 0.75rem;
    font-weight: 700;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-xl);
    text-transform: uppercase;
    letter-spacing: 0.8px;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.status-livre, .status-aberto {
    background: var(--success-gradient);
    color: var(--white);
    border: 1px solid rgba(5, 150, 105, 0.3);
}

.status-ocupada, .status-fechada {
    background: var(--danger-gradient);
    color: var(--white);
    border: 1px solid rgba(220, 38, 38, 0.3);
}

.status-reservada {
    background: var(--warning-gradient);
    color: var(--white);
    border: 1px solid rgba(217, 119, 6, 0.3);
}

.status-badge:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Buttons */
.btn {
    border-radius: var(--radius-lg);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    border: none;
    font-size: 0.875rem;
    letter-spacing: 0.025em;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--primary-gradient);
    color: var(--white);
    box-shadow: var(--shadow-blue);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-blue-dark) 0%, var(--primary-blue) 50%, var(--primary-blue-light) 100%);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-secondary {
    background: var(--secondary-gradient);
    color: var(--white);
}

.btn-success {
    background: var(--success-gradient);
    color: var(--white);
}

.btn-warning {
    background: var(--warning-gradient);
    color: var(--white);
}

.btn-danger {
    background: var(--danger-gradient);
    color: var(--white);
}

.btn-outline-primary {
    border: 2px solid var(--primary-blue);
    color: var(--primary-blue);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-gradient);
    color: var(--white);
    border-color: transparent;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn:active {
    transform: translateY(0);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1rem;
}

/* Statistics Cards */
.stats-card {
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.stats-card h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card p {
    font-size: 1rem;
    opacity: 0.9;
}

/* Filters Section */
.filters-section {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-light);
}

/* Mesa Header */
.mesa-header {
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-light);
}

/* Consumo Items */
.consumo-item {
    border-left: 4px solid #007bff;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: var(--transition);
}

.consumo-item:hover {
    background-color: #e9ecef;
    transform: translateX(5px);
    box-shadow: var(--shadow-light);
}

/* Action Buttons */
.action-buttons {
    gap: 0.5rem;
}

.btn-action {
    border-radius: 20px;
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
    min-width: 40px;
}

/* Total Section */
.total-section {
    background: var(--success-gradient);
    color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--shadow-light);
}

.total-section h2 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 0;
}

/* Modals */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
}

.modal-header {
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* Forms */
.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Notifications */
.alert {
    border: none;
    border-radius: 10px;
    font-weight: 500;
}

/* Navigation */
.navbar {
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.navbar-brand {
    transition: var(--transition);
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.75rem 1rem !important;
    border-radius: var(--radius);
    transition: var(--transition);
    margin: 0 0.25rem;
}

.navbar-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white) !important;
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: var(--white) !important;
}

/* Clock */
.clock {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    font-size: 1.1rem;
}

/* Modals */
.modal-content {
    border: none;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-2xl);
    backdrop-filter: blur(10px);
}

.modal-header {
    background: var(--primary-gradient);
    color: var(--white);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    border: none;
    padding: 1.5rem 2rem;
}

.modal-title {
    font-weight: 700;
    font-size: 1.25rem;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border: none;
    padding: 1rem 2rem 2rem;
}

/* Forms */
.form-control, .form-select {
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: var(--transition);
    background: var(--gray-50);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-blue-light);
    box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.15);
    background: var(--white);
}

.form-label {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.input-group {
    margin-bottom: 1rem;
}

.input-group-text {
    background: var(--primary-gradient);
    color: var(--white);
    border: 2px solid var(--primary-blue);
    border-radius: var(--radius-lg) 0 0 var(--radius-lg);
    font-weight: 600;
}

/* Tables */
.table {
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow);
    background: var(--white);
}

.table thead th {
    background: var(--primary-gradient);
    color: var(--white);
    border: none;
    font-weight: 700;
    padding: 1rem;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.05em;
}

.table tbody td {
    padding: 1rem;
    border-color: var(--gray-200);
    vertical-align: middle;
}

.table tbody tr:hover {
    background: var(--gray-50);
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--radius-lg);
    padding: 1rem 1.5rem;
    font-weight: 500;
    box-shadow: var(--shadow);
}

.alert-primary {
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
    color: var(--primary-blue);
    border-left: 4px solid var(--primary-blue);
}

.alert-success {
    background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
    color: #059669;
    border-left: 4px solid #059669;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(217, 119, 6, 0.1) 0%, rgba(245, 158, 11, 0.1) 100%);
    color: #d97706;
    border-left: 4px solid #d97706;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 38, 38, 0.1) 0%, rgba(239, 68, 68, 0.1) 100%);
    color: #dc2626;
    border-left: 4px solid #dc2626;
}

/* Responsive Design */
@media (max-width: 768px) {
    .mesa-header {
        padding: 1.5rem;
        text-align: center;
    }

    .stats-card {
        margin-bottom: 1rem;
    }

    .filters-section {
        padding: 1.5rem;
    }

    .consumo-item {
        padding: 0.75rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }

    .btn-action {
        width: 100%;
    }

    .page-title {
        font-size: 2rem;
    }

    .display-4 {
        font-size: 2rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Print Styles */
@media print {
    .btn, .modal, .navbar, .filters-section {
        display: none !important;
    }
    
    .mesa-header {
        background: #333 !important;
        color: white !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
