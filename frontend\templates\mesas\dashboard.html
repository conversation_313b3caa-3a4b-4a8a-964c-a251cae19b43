{% extends 'base.html' %}

{% block title %}Dashboard - <PERSON>{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    .mesa-card {
        border-radius: 10px;
        transition: all 0.3s ease;
        cursor: pointer;
        margin-bottom: 1rem;
    }
    .mesa-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    .status-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
    }
    .status-livre {
        background-color: #28a745;
        color: white;
    }
    .status-ocupada {
        background-color: #dc3545;
        color: white;
    }
    .status-reservada {
        background-color: #ffc107;
        color: #212529;
    }
    .filters-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="col-12">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center my-4">
        <h2><i class="bi bi-grid-3x3-gap"></i> Dashboard de Mesas</h2>
        <button class="btn btn-primary" onclick="refreshData()">
            <i class="bi bi-arrow-clockwise"></i> Atualizar
        </button>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card text-center">
                <h3 id="total-mesas">{{ mesas|length }}</h3>
                <p class="mb-0">Total de Mesas</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <h3 id="mesas-ocupadas">0</h3>
                <p class="mb-0">Mesas Ocupadas</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <h3 id="mesas-livres">0</h3>
                <p class="mb-0">Mesas Livres</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <h3 id="faturamento-total">R$ 0,00</h3>
                <p class="mb-0">Faturamento</p>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filters-section">
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">Filtrar por Mesa:</label>
                <input type="text" class="form-control" id="filtro-mesa" placeholder="Ex: M1, M2...">
            </div>
            <div class="col-md-3">
                <label class="form-label">Filtrar por Cliente:</label>
                <input type="text" class="form-control" id="filtro-cliente" placeholder="Nome do cliente">
            </div>
            <div class="col-md-3">
                <label class="form-label">Filtrar por Status:</label>
                <select class="form-select" id="filtro-status">
                    <option value="">Todos os status</option>
                    <option value="livre">Livre</option>
                    <option value="ocupada">Ocupada</option>
                    <option value="reservada">Reservada</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button class="btn btn-outline-primary w-100" onclick="aplicarFiltros()">
                    <i class="bi bi-funnel"></i> Aplicar Filtros
                </button>
            </div>
        </div>
    </div>

    <!-- Mesas Grid -->
    <div class="row" id="mesas-container">
        {% for mesa in mesas %}
        <div class="col-lg-3 col-md-4 col-sm-6 mesa-item" 
             data-mesa-id="{{ mesa.id }}" 
             data-cliente="{{ mesa.cliente|lower }}" 
             data-status="{{ mesa.status|lower }}">
            <div class="card mesa-card" onclick="abrirDetalheMesa('{{ mesa.id }}')">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h5 class="card-title mb-0">{{ mesa.nome }}</h5>
                        <span class="status-badge status-{{ mesa.status|lower }}">
                            {{ mesa.status|title }}
                        </span>
                    </div>
                    
                    {% if mesa.cliente %}
                        <p class="card-text">
                            <i class="bi bi-person"></i> {{ mesa.cliente }}
                        </p>
                    {% endif %}
                    
                    {% if mesa.cpf_cnpj %}
                        <p class="card-text">
                            <i class="bi bi-card-text"></i> {{ mesa.cpf_cnpj }}
                        </p>
                    {% endif %}
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">{{ mesa.id }}</small>
                        <strong class="text-success">R$ {{ mesa.total|floatformat:2 }}</strong>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="alert alert-info text-center">
                <i class="bi bi-info-circle"></i> Nenhuma mesa encontrada.
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Atualizar estatísticas
    function atualizarEstatisticas() {
        const mesas = document.querySelectorAll('.mesa-item');
        let ocupadas = 0, livres = 0, faturamento = 0;
        
        mesas.forEach(mesa => {
            const status = mesa.dataset.status;
            const valor = parseFloat(mesa.querySelector('.text-success').textContent.replace('R$ ', '').replace(',', '.')) || 0;
            
            if (status === 'ocupada') ocupadas++;
            else if (status === 'livre') livres++;
            
            faturamento += valor;
        });
        
        document.getElementById('mesas-ocupadas').textContent = ocupadas;
        document.getElementById('mesas-livres').textContent = livres;
        document.getElementById('faturamento-total').textContent = `R$ ${faturamento.toFixed(2).replace('.', ',')}`;
    }
    
    // Aplicar filtros
    function aplicarFiltros() {
        const filtroMesa = document.getElementById('filtro-mesa').value.toLowerCase();
        const filtroCliente = document.getElementById('filtro-cliente').value.toLowerCase();
        const filtroStatus = document.getElementById('filtro-status').value.toLowerCase();
        
        document.querySelectorAll('.mesa-item').forEach(mesa => {
            const mesaId = mesa.dataset.mesaId.toLowerCase();
            const cliente = mesa.dataset.cliente;
            const status = mesa.dataset.status;
            
            const mostrar = (!filtroMesa || mesaId.includes(filtroMesa)) &&
                           (!filtroCliente || cliente.includes(filtroCliente)) &&
                           (!filtroStatus || status === filtroStatus);
            
            mesa.style.display = mostrar ? 'block' : 'none';
        });
    }
    
    // Abrir detalhes da mesa
    function abrirDetalheMesa(mesaId) {
        window.location.href = `/mesa/${mesaId}/`;
    }
    
    // Atualizar dados
    function refreshData() {
        location.reload();
    }
    
    // Inicializar
    document.addEventListener('DOMContentLoaded', function() {
        atualizarEstatisticas();
    });
</script>
{% endblock %}
