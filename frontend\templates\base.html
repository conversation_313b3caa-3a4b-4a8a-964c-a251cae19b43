<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Alto da Serra - Sistema de Gestão{% endblock %}</title>

    <!-- Favicon -->
    {% load static %}
    <link rel="icon" type="image/png" href="{% static 'images/logofornecedor.png' %}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom CSS File -->
    <link href="{% static 'css/custom.css' %}" rel="stylesheet">

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: var(--primary-gradient); box-shadow: var(--shadow-lg);">
        <div class="container-fluid">
            <a class="navbar-brand d-flex align-items-center" href="{% url 'mesas:dashboard' %}">
                <img src="{% static 'images/logocliente.png' %}" alt="Alto da Serra" height="40" class="me-2" style="border-radius: var(--radius);">
                <div>
                    <div style="font-weight: 700; font-size: 1.25rem; line-height: 1;">Alto da Serra</div>
                    <div style="font-size: 0.75rem; opacity: 0.9; font-weight: 400;">Sistema de Gestão</div>
                </div>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'mesas:dashboard' %}">
                            <i class="bi bi-grid-3x3-gap"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'mesas:produtos' %}">
                            <i class="bi bi-box-seam"></i> Produtos
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'mesas:estoque' %}">
                            <i class="bi bi-boxes"></i> Estoque
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'mesas:cozinha' %}">
                            <i class="bi bi-egg-fried"></i> Cozinha
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="navbar-text clock" id="clock"></span>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> {{ usuario_nome|default:"Usuário" }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><span class="dropdown-item-text">Tipo: {{ usuario_tipo|default:"N/A" }}</span></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'mesas:logout' %}">
                                <i class="bi bi-box-arrow-right"></i> Sair
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Messages -->
    {% if messages %}
        <div class="container-fluid mt-2">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Footer -->
    <footer class="mt-5 py-4" style="background: var(--gray-900); color: var(--gray-300);">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <img src="{% static 'images/logofornecedor.png' %}" alt="LIM Soluções" height="32" class="me-3" style="border-radius: var(--radius);">
                        <div>
                            <div style="font-weight: 600; font-size: 0.9rem;">Desenvolvido por LIM Soluções</div>
                            <div style="font-size: 0.75rem; opacity: 0.7;">Tecnologia e Inovação para seu negócio</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 text-md-end">
                    <div style="font-size: 0.8rem; opacity: 0.7;">
                        © {{ "now"|date:"Y" }} Alto da Serra - Todos os direitos reservados
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Clock Script -->
    <script>
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('pt-BR');
            const dateString = now.toLocaleDateString('pt-BR');
            document.getElementById('clock').textContent = `${dateString} ${timeString}`;
        }
        
        updateClock();
        setInterval(updateClock, 1000);
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
